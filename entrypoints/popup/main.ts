import { toolRegistry } from '../../utils/tool-registry';
import { settingsManager } from '../../utils/settings-manager';
import { categoryManager } from '../../utils/category-manager';
import { notificationManager } from '../../utils/notification-manager';
import type { Tool, NewTabLaunchData } from '../../utils/tool-template';
import { styleManager } from '../../utils/style-manager';
import { updateManager } from '../../utils/update-manager';
import { updateNotificationManager } from '../../utils/update-notification';
import { UpdateDetailModal } from '../../utils/update-components';

import { XuidTool } from '../../tools/xuid';
import { AlertParserTool } from '../../tools/alert-parser';
import { TaskListTool } from '../../tools/task-list';
import { ApiDiffTool } from '../../tools/api-diff-tool';
// 工具注册表已从utils/tool-registry.ts导入

// 工具管理器
class ToolManager {
  private container: HTMLElement;
  private searchInput: HTMLInputElement;
  private categoriesNav: HTMLElement;
  private categoryManageBtn: HTMLElement;
  private currentCategory = 'all';
  private currentQuery = '';

  constructor() {
    this.container = document.getElementById('tools-container')!;
    this.searchInput = document.getElementById('search-input') as HTMLInputElement;
    this.categoriesNav = document.getElementById('categories-nav')!;
    this.categoryManageBtn = document.getElementById('category-manage-btn')!;

    this.init();
  }

  private async init() {
    // 加载样式文件
    try {
      // 注册popup样式模块
      styleManager.registerModule({
        name: 'popup',
        path: '/entrypoints/popup/style.css',
        dependencies: ['design-tokens']
      });

      // 注册alert-parser样式模块
      styleManager.registerModule({
        name: 'alert-parser',
        path: '/styles/alert-parser.css',
        dependencies: ['design-tokens', 'components']
      });

      // 注册update样式模块
      styleManager.registerModule({
        name: 'update',
        path: '/styles/update.css',
        dependencies: ['design-tokens', 'components']
      });

      // 注册task-list样式模块
      styleManager.registerModule({
        name: 'task-list',
        path: '/styles/task-list.css',
        dependencies: ['design-tokens', 'components']
      });

      // 注册api-diff样式模块
      styleManager.registerModule({
        name: 'api-diff',
        path: '/tools/api-diff/styles/api-diff.css',
        dependencies: ['design-tokens', 'components']
      });

      // 加载样式模块
      await styleManager.loadModule('popup');
      await styleManager.loadModule('alert-parser');
      await styleManager.loadModule('update');
      await styleManager.loadModule('api-diff');
      console.log('✅ Popup样式加载成功');
    } catch (error) {
      console.warn('❌ Popup样式加载失败:', error);
    }

    // 初始化分类管理器
    await categoryManager.init();

    // 初始化工具注册表
    await toolRegistry.init();

    // 初始化更新管理器
    await updateManager.loadUpdateStates();

    // 从设置管理器获取默认分类
    const defaultCategory = settingsManager.getSetting('defaultCategory');

    // 验证默认分类是否存在，如果存在则使用它，否则使用 'all'
    if (categoryManager.exists(defaultCategory)) {
      this.currentCategory = defaultCategory;
    } else {
      this.currentCategory = 'all';
      // 如果设置的默认分类不存在，静默更新为 'all'
      settingsManager.updateSetting('defaultCategory', 'all').catch(console.error);
    }

    // 渲染分类导航
    this.renderCategories();

    // 搜索功能
    this.searchInput.addEventListener('input', (e) => {
      this.currentQuery = (e.target as HTMLInputElement).value;
      this.renderTools();
    });

    // 分类管理按钮
    this.categoryManageBtn.addEventListener('click', () => {
      this.openCategoryManager();
    });

    // 设置按钮
    const settingsBtn = document.getElementById('settings-btn');
    settingsBtn?.addEventListener('click', () => {
      this.openSettings();
    });

    // 监听工具变化
    toolRegistry.onChange(() => {
      this.renderTools();
    });

    // 监听分类变化
    categoryManager.addListener(() => {
      this.renderCategories();
      this.renderTools();
    });

    // 监听更新状态变化
    document.addEventListener('updateStatesChanged', (event: any) => {
      this.handleUpdateStatesChanged(event.detail);
    });

    // 监听显示更新详情事件
    document.addEventListener('showUpdateDetails', () => {
      this.showUpdateDetails();
    });

    // 监听手动检查更新事件
    document.addEventListener('manualUpdateCheck', () => {
      this.handleManualUpdateCheck();
    });

    // 监听手动检查更新完成事件
    document.addEventListener('manualUpdateCheckCompleted', (event: any) => {
      this.handleUpdateStatesChanged(event.detail);
    });

    // 初始渲染
    this.renderTools();
  }

  private renderTools() {
    let tools = toolRegistry.getByCategory(this.currentCategory);

    if (this.currentQuery) {
      tools = toolRegistry.search(this.currentQuery);
    }

    if (tools.length === 0) {
      this.renderEmptyState();
      return;
    }

    this.container.innerHTML = tools.map(tool => this.createToolCard(tool)).join('');

    // 添加点击事件
    this.container.querySelectorAll('.tool-card').forEach((card, index) => {
      const tool = tools[index];

      // 工具卡片点击事件
      card.addEventListener('click', (e) => {
        const target = e.target as HTMLElement;
        // 如果点击的是按钮，不执行工具
        if (target.classList.contains('tool-settings-btn')) {
          return;
        }
        if (tool.enabled) {
          this.executeTool(tool);
        }
      });

      // 工具设置按钮点击事件
      const settingsBtn = card.querySelector('.tool-settings-btn');
      if (settingsBtn) {
        settingsBtn.addEventListener('click', (e) => {
          e.stopPropagation();
          this.openToolSettings(tool);
        });
      }
    });

    // 异步检查更新
    this.checkUpdatesForTools(tools);

    // 添加拖拽功能
    this.initDragAndDrop();

    // 显示更新通知
    this.showUpdateNotifications();
  }

  private createToolCard(tool: Tool): string {
    const disabledClass = tool.enabled ? '' : 'disabled';
    const versionString = `v${tool.version.major}.${tool.version.minor}.${tool.version.patch}`;

    // 显示工具的分类标签（显示分类名称而不是ID）
    const categoriesHtml = tool.categories && tool.categories.length > 0
      ? `<div class="tool-categories">${tool.categories.filter(cat => cat !== 'all').map(categoryId => {
          const category = categoryManager.getById(categoryId);
          return `<span class="category-tag">${category ? category.name : categoryId}</span>`;
        }).join('')}</div>`
      : '';

    // 更新徽章将通过异步方式添加，默认不显示任何徽章
    const settingsButtonHtml = `<button class="tool-settings-btn" data-tool-id="${tool.id}" title="工具设置">⚙️</button>`;

    return `
      <div class="tool-card ${disabledClass}" data-tool-id="${tool.id}" draggable="true">
        <div class="drag-handle" title="拖拽排序">⋮⋮</div>
        <span class="tool-icon">${tool.icon}</span>
        <div class="tool-name">${tool.name}</div>
        <div class="tool-description">${tool.description}</div>
        ${categoriesHtml}
        <div class="tool-footer">
          <span class="tool-version">${versionString}</span>
          <div class="tool-actions">
            ${settingsButtonHtml}
          </div>
        </div>
      </div>
    `;
  }



  private async checkUpdatesForTools(tools: Tool[]) {
    try {
      // 检查是否启用自动检查更新
      const autoCheckUpdates = settingsManager.getSetting('autoCheckUpdates');
      if (!autoCheckUpdates) {
        // 即使不自动检查，也要显示已有的更新状态
        this.updateToolBadges();
        return;
      }

      // 检查是否需要进行更新检查（基于时间间隔）
      const shouldCheck = await this.shouldCheckForUpdates();
      if (!shouldCheck) {
        // 如果不需要检查，显示已有的更新状态
        this.updateToolBadges();
        return;
      }

      console.log('开始检查工具更新...');

      // 使用更新管理器检查更新
      await updateManager.checkAndNotifyUpdates(tools);

      // 更新最后检查时间
      await this.updateLastCheckTime();

    } catch (error) {
      console.error('批量检查更新失败:', error);
    }
  }



  /**
   * 处理更新状态变化
   */
  private handleUpdateStatesChanged(detail: any): void {
    this.showUpdateNotifications();

    // 更新工具卡片上的徽章
    this.updateToolBadges();
  }

  /**
   * 显示更新详情
   */
  private showUpdateDetails(): void {
    const updateStates = updateManager.getAllUpdateStates();
    const availableUpdates = new Map();

    // 筛选出有更新且未被忽略的工具
    for (const [toolId, state] of updateStates) {
      if (state.hasUpdate && !state.userDismissed && state.updateInfo) {
        // 使用新的忽略逻辑检查
        if (!updateManager.isUpdateIgnored(state, state.updateInfo)) {
          availableUpdates.set(toolId, state.updateInfo);
        }
      }
    }

    if (availableUpdates.size === 0) {
      notificationManager.info('暂无可用更新');
      return;
    }

    // 创建并显示更新详情模态框
    const modal = new UpdateDetailModal(availableUpdates);
    modal.show();
  }

  /**
   * 处理手动检查更新
   */
  private async handleManualUpdateCheck(): Promise<void> {
    try {
      const tools = toolRegistry.getAll();
      // 传入forceCheck=true来强制检查，忽略自动检查设置
      await updateManager.checkAndNotifyUpdates(tools, true);
      notificationManager.success('更新检查完成');
    } catch (error) {
      console.error('手动检查更新失败:', error);
      notificationManager.error('检查更新失败，请稍后重试');
    }
  }

  /**
   * 显示更新通知
   */
  private showUpdateNotifications(): void {
    const updateCount = updateManager.getUpdateCount();

    if (updateCount > 0) {
      // 显示更新横幅
      updateNotificationManager.showUpdateBanner(updateCount);
    } else {
      // 隐藏更新横幅
      updateNotificationManager.hideBanner();
    }
  }

  private updateToolBadges(updateResults?: Map<string, any>) {
    // 先清除所有现有的更新徽章
    this.container.querySelectorAll('.update-badge').forEach(badge => badge.remove());

    // 如果没有传入updateResults，从updateManager获取
    if (!updateResults) {
      const updateStates = updateManager.getAllUpdateStates();
      updateStates.forEach((state, toolId) => {
        if (state.hasUpdate && !state.userDismissed && state.updateInfo) {
          // 使用新的忽略逻辑检查
          if (!updateManager.isUpdateIgnored(state, state.updateInfo)) {
            this.addUpdateBadge(toolId);
          }
        }
      });
    } else {
      // 为有更新的工具添加徽章
      updateResults.forEach((result, toolId) => {
        if (result.hasUpdate) {
          this.addUpdateBadge(toolId);
        }
      });
    }
  }

  private addUpdateBadge(toolId: string): void {
    const toolCard = this.container.querySelector(`[data-tool-id="${toolId}"]`);
    if (toolCard && !toolCard.querySelector('.update-badge')) {
      const updateBadge = document.createElement('span');
      updateBadge.className = 'update-badge';
      updateBadge.textContent = 'NEW';
      updateBadge.title = '有更新可用';
      toolCard.appendChild(updateBadge);
    }
  }

  // 检查是否需要进行更新检查
  private async shouldCheckForUpdates(): Promise<boolean> {
    try {
      const result = await browser.storage.local.get(['lastUpdateCheck']);
      const lastCheckTime = result.lastUpdateCheck || 0;
      const currentTime = Date.now();
      const checkInterval = settingsManager.getSetting('updateCheckInterval') * 60 * 1000; // 转换为毫秒

      return (currentTime - lastCheckTime) >= checkInterval;
    } catch (error) {
      console.error('检查更新时间失败:', error);
      return true; // 如果出错，默认进行检查
    }
  }

  // 更新最后检查时间
  private async updateLastCheckTime(): Promise<void> {
    try {
      await browser.storage.local.set({ lastUpdateCheck: Date.now() });
    } catch (error) {
      console.error('更新检查时间失败:', error);
    }
  }



  private renderEmptyState() {
    const emptyMessage = this.currentQuery
      ? `没有找到包含 "${this.currentQuery}" 的工具`
      : '该分类下暂无工具';

    this.container.innerHTML = `
      <div class="empty-state">
        <div class="empty-state-icon">🔍</div>
        <div class="empty-state-text">${emptyMessage}</div>
        <div class="empty-state-subtext">尝试搜索其他关键词或切换分类</div>
      </div>
    `;
  }

  private async executeTool(tool: Tool) {
    try {
      console.log(`🔍 获取工具实例: ${tool.id}`);
      const toolInstance = toolRegistry.getById(tool.id);

      if (!toolInstance) {
        throw new Error(`工具 ${tool.name} 未找到`);
      }

      // 检查是否需要跳转到newtab
      if (toolInstance.displayMode === 'newtab') {
        console.log(`🚀 工具 ${tool.name} 需要在New Tab中执行，开始跳转...`);
        await this.openToolInNewTab(toolInstance);
        return;
      }

      // 原有popup执行逻辑
      if (typeof toolInstance.action === 'function') {
        console.log(`🎬 在popup中执行工具: ${toolInstance.name}`);
        await toolInstance.action();
        console.log(`✅ 工具 ${tool.name} 执行完成`);
      } else {
        throw new Error(`工具 ${tool.name} 的 action 方法不存在`);
      }

    } catch (error) {
      console.error(`❌ 执行工具 ${tool.name} 时出错:`, error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      notificationManager.error(`执行工具失败: ${errorMessage}`);
    }
  }

  private openSettings() {
    try {
      const settingsModal = settingsManager.createSettingsModal();
      document.body.appendChild(settingsModal);
    } catch (error) {
      console.error('打开设置失败:', error);
      notificationManager.error('打开设置失败，请重试');
    }
  }

  private async openToolInNewTab(tool: Tool) {
    try {
      // 1. 准备传递数据
      const launchData: NewTabLaunchData = {
        toolId: tool.id,
        timestamp: Date.now(),
        data: tool.newtabData || {},
        source: 'popup'
      };

      console.log('📦 准备New Tab启动数据:', launchData);

      // 2. 存储到Chrome Storage
      await browser.storage.local.set({
        'newtab-tool-launch': launchData
      });

      // 3. 显示跳转提示
      notificationManager.info(`正在新标签页中打开 ${tool.name}...`);

      // 4. 打开newtab页面
      const newTabUrl = browser.runtime.getURL('newtab.html');
      console.log('🌐 打开New Tab页面:', newTabUrl);

      await browser.tabs.create({
        url: newTabUrl,
        active: true
      });

      // 5. 延迟关闭popup，给用户反馈时间
      setTimeout(() => {
        window.close();
      }, 500);

    } catch (error) {
      console.error('❌ 打开New Tab失败:', error);
      notificationManager.error('打开新标签页失败，请重试');

      // 回退到popup执行
      if (typeof tool.action === 'function') {
        console.log('🔄 回退到popup执行...');
        await tool.action();
      }
    }
  }

  // 渲染分类导航
  private renderCategories() {
    const categories = categoryManager.getAll();

    // 清空现有的分类按钮（保留管理按钮）
    const existingButtons = this.categoriesNav.querySelectorAll('.category-btn');
    existingButtons.forEach(btn => btn.remove());

    // 创建分类按钮
    categories.forEach(category => {
      const button = document.createElement('button');
      button.className = 'category-btn';
      button.dataset.category = category.id;
      button.textContent = category.name;

      if (category.icon) {
        button.innerHTML = `${category.icon} ${category.name}`;
      }

      // 设置当前激活状态
      if (category.id === this.currentCategory) {
        button.classList.add('active');
      }

      // 添加点击事件
      button.addEventListener('click', () => {
        // 移除所有按钮的激活状态
        this.categoriesNav.querySelectorAll('.category-btn').forEach(btn => {
          btn.classList.remove('active');
        });

        // 激活当前按钮
        button.classList.add('active');
        this.currentCategory = category.id;
        this.renderTools();
      });

      // 插入到管理按钮之前
      this.categoriesNav.insertBefore(button, this.categoryManageBtn);
    });
  }

  // 打开分类管理器
  private openCategoryManager() {
    const modal = this.createCategoryManagerModal();
    document.body.appendChild(modal);
  }

  // 创建分类管理模态框
  private createCategoryManagerModal(): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
      <div class="modal-content category-manager-modal">
        <div class="modal-header">
          <h3>分类管理</h3>
          <button class="modal-close-btn" type="button">×</button>
        </div>
        <div class="modal-body">
          <div class="category-list" id="category-list">
            <!-- 分类列表将在这里动态生成 -->
          </div>
          <div class="add-category-section">
            <div class="input-group">
              <input type="text" id="new-category-name" placeholder="输入新分类名称" class="form-control">
              <button id="add-category-btn" class="btn btn-primary">添加分类</button>
            </div>
          </div>
        </div>
      </div>
    `;

    // 渲染现有分类
    this.renderCategoryList(modal);

    // 绑定事件
    this.bindCategoryManagerEvents(modal);

    return modal;
  }

  // 渲染分类列表
  private renderCategoryList(modal: HTMLElement) {
    const categoryList = modal.querySelector('#category-list') as HTMLElement;
    const categories = categoryManager.getAll();

    categoryList.innerHTML = categories.map(category => `
      <div class="category-item" data-category-id="${category.id}">
        <div class="category-info">
          <span class="category-icon">${category.icon || '📁'}</span>
          <span class="category-name">${category.name}</span>
        </div>
        <div class="category-actions">
          ${category.id !== 'all' ? `
            <button class="btn btn-sm btn-secondary edit-category-btn" data-category-id="${category.id}">编辑</button>
            <button class="btn btn-sm btn-danger delete-category-btn" data-category-id="${category.id}">删除</button>
          ` : `
            <span class="text-muted">默认分类</span>
          `}
        </div>
      </div>
    `).join('');
  }

  // 绑定分类管理器事件
  private bindCategoryManagerEvents(modal: HTMLElement) {
    // 关闭模态框
    const closeBtn = modal.querySelector('.modal-close-btn');
    const overlay = modal;

    const closeModal = () => {
      document.body.removeChild(modal);
    };

    closeBtn?.addEventListener('click', closeModal);
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        closeModal();
      }
    });

    // 添加分类
    const addBtn = modal.querySelector('#add-category-btn');
    const nameInput = modal.querySelector('#new-category-name') as HTMLInputElement;

    const addCategory = async () => {
      const name = nameInput.value.trim();
      if (!name) {
        notificationManager.warning('请输入分类名称');
        return;
      }

      // 检查分类名称是否已存在
      if (categoryManager.existsByName(name)) {
        notificationManager.warning('分类名称已存在，请使用其他名称');
        return;
      }

      try {
        const id = categoryManager.generateId(name);
        await categoryManager.addCategory({
          id,
          name,
          icon: '📁',
          order: categoryManager.getCount()
        });

        nameInput.value = '';
        this.renderCategoryList(modal);
        notificationManager.success('分类添加成功');
      } catch (error) {
        console.error('添加分类失败:', error);
        notificationManager.error('添加分类失败: ' + (error as Error).message);
      }
    };

    addBtn?.addEventListener('click', addCategory);
    nameInput?.addEventListener('keypress', (e) => {
      if (e.key === 'Enter') {
        addCategory();
      }
    });

    // 删除分类
    modal.addEventListener('click', async (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('delete-category-btn')) {
        const categoryId = target.dataset.categoryId;
        if (!categoryId) return;

        const confirmed = await notificationManager.confirm({
          title: '删除分类',
          message: `确定要删除分类"${categoryId}"吗？\n\n注意：此操作会自动清理所有使用此分类的工具。`,
          confirmText: '删除',
          cancelText: '取消',
          type: 'danger'
        });

        if (confirmed) {
          try {
            await categoryManager.removeCategory(categoryId);
            this.renderCategoryList(modal);
            notificationManager.success('分类删除成功');
          } catch (error) {
            console.error('删除分类失败:', error);
            notificationManager.error('删除分类失败: ' + (error as Error).message);
          }
        }
      }
    });

    // 编辑分类（简单实现）
    modal.addEventListener('click', async (e) => {
      const target = e.target as HTMLElement;
      if (target.classList.contains('edit-category-btn')) {
        const categoryId = target.dataset.categoryId;
        if (!categoryId) return;

        const category = categoryManager.getById(categoryId);
        if (!category) return;

        const newName = await notificationManager.prompt({
          title: '编辑分类',
          message: '请输入新的分类名称:',
          defaultValue: category.name,
          confirmText: '保存',
          cancelText: '取消'
        });

        if (newName && newName.trim() && newName.trim() !== category.name) {
          // 检查新名称是否已存在
          if (categoryManager.existsByName(newName.trim())) {
            notificationManager.warning('分类名称已存在，请使用其他名称');
            return;
          }

          try {
            await categoryManager.updateCategory(categoryId, { name: newName.trim() });
            this.renderCategoryList(modal);
            notificationManager.success('分类更新成功');
          } catch (error) {
            console.error('更新分类失败:', error);
            notificationManager.error('更新分类失败: ' + (error as Error).message);
          }
        }
      }
    });
  }

  // 初始化拖拽功能
  private initDragAndDrop() {
    let draggedElement: HTMLElement | null = null;
    let draggedToolId: string | null = null;

    // 为所有工具卡片添加拖拽事件
    this.container.querySelectorAll('.tool-card').forEach(card => {
      const toolCard = card as HTMLElement;

      // 拖拽开始
      toolCard.addEventListener('dragstart', (e) => {
        draggedElement = toolCard;
        draggedToolId = toolCard.dataset.toolId || null;
        toolCard.classList.add('dragging');

        // 设置拖拽数据
        if (e.dataTransfer) {
          e.dataTransfer.effectAllowed = 'move';
          e.dataTransfer.setData('text/html', toolCard.outerHTML);
        }
      });

      // 拖拽结束
      toolCard.addEventListener('dragend', () => {
        toolCard.classList.remove('dragging');
        // 清理所有拖拽相关的样式
        this.container.querySelectorAll('.tool-card').forEach(card => {
          card.classList.remove('drag-over', 'drag-over-top', 'drag-over-bottom');
        });
      });

      // 拖拽进入
      toolCard.addEventListener('dragenter', (e) => {
        e.preventDefault();
        if (draggedElement && draggedElement !== toolCard) {
          toolCard.classList.add('drag-over');
        }
      });

      // 拖拽悬停
      toolCard.addEventListener('dragover', (e) => {
        e.preventDefault();
        if (draggedElement && draggedElement !== toolCard) {
          const rect = toolCard.getBoundingClientRect();
          const midY = rect.top + rect.height / 2;

          // 根据鼠标位置决定插入位置
          if (e.clientY < midY) {
            toolCard.classList.remove('drag-over-bottom');
            toolCard.classList.add('drag-over-top');
          } else {
            toolCard.classList.remove('drag-over-top');
            toolCard.classList.add('drag-over-bottom');
          }
        }
      });

      // 拖拽离开
      toolCard.addEventListener('dragleave', (e) => {
        // 只有当鼠标真正离开元素时才移除样式
        const rect = toolCard.getBoundingClientRect();
        if (e.clientX < rect.left || e.clientX > rect.right ||
            e.clientY < rect.top || e.clientY > rect.bottom) {
          toolCard.classList.remove('drag-over', 'drag-over-top', 'drag-over-bottom');
        }
      });

      // 拖拽放置
      toolCard.addEventListener('drop', async (e) => {
        e.preventDefault();

        if (draggedElement && draggedToolId && draggedElement !== toolCard) {
          const targetToolId = toolCard.dataset.toolId;
          if (targetToolId) {
            // 使用更可靠的逻辑：基于工具的原始位置关系来判断插入方向
            const tools = toolRegistry.getAll();
            const draggedIndex = tools.findIndex(t => t.id === draggedToolId);
            const targetIndex = tools.findIndex(t => t.id === targetToolId);

            // 如果拖拽的工具原来在目标工具的左边，插入到目标工具之后
            // 如果拖拽的工具原来在目标工具的右边，插入到目标工具之前
            const insertAfter = draggedIndex < targetIndex;

            console.log(`Drop事件: 拖拽索引=${draggedIndex}, 目标索引=${targetIndex}, insertAfter=${insertAfter}`);

            await this.handleToolReorder(draggedToolId, targetToolId, insertAfter);
          }
        }

        // 清理样式
        toolCard.classList.remove('drag-over', 'drag-over-top', 'drag-over-bottom');
      });
    });
  }

  // 处理工具重新排序
  private async handleToolReorder(draggedToolId: string, targetToolId: string, insertAfter: boolean) {
    try {
      const tools = toolRegistry.getAll();
      const draggedTool = tools.find(t => t.id === draggedToolId);
      const targetTool = tools.find(t => t.id === targetToolId);

      if (!draggedTool || !targetTool || draggedToolId === targetToolId) {
        console.log('拖拽取消：工具不存在或相同');
        return;
      }

      console.log(`拖拽开始: ${draggedToolId} -> ${targetToolId} (${insertAfter ? '之后' : '之前'})`);
      console.log('原始工具顺序:', tools.map(t => t.id));

      // 创建新的排序数组
      const newOrder: Tool[] = [];
      let draggedToolInserted = false;

      for (const tool of tools) {
        if (tool.id === draggedToolId) {
          // 跳过被拖拽的工具，稍后插入
          continue;
        }

        if (tool.id === targetToolId) {
          if (!insertAfter) {
            // 插入到目标工具之前
            newOrder.push(draggedTool);
            draggedToolInserted = true;
          }
          newOrder.push(tool);
          if (insertAfter) {
            // 插入到目标工具之后
            newOrder.push(draggedTool);
            draggedToolInserted = true;
          }
        } else {
          newOrder.push(tool);
        }
      }

      // 确保被拖拽的工具被插入了
      if (!draggedToolInserted) {
        console.error('错误：被拖拽的工具没有被插入到新数组中');
        return;
      }

      console.log('新的工具顺序:', newOrder.map(t => t.id));

      // 检查数组长度是否正确
      if (newOrder.length !== tools.length) {
        console.error(`错误：数组长度不匹配。原始: ${tools.length}, 新的: ${newOrder.length}`);
        return;
      }

      // 重新分配position值
      const positionUpdates: { id: string; position: number }[] = [];
      console.log('重新分配position值:');
      newOrder.forEach((tool, index) => {
        const newPosition = index * 1000; // 使用1000的间隔
        console.log(`  ${tool.id}: ${tool.position} -> ${newPosition}`);
        if (tool.position !== newPosition) {
          positionUpdates.push({ id: tool.id, position: newPosition });
        }
      });

      console.log('需要更新的位置:', positionUpdates);

      // 强制更新所有工具的位置，确保排序被保存
      if (positionUpdates.length > 0) {
        await toolRegistry.updateToolPositions(positionUpdates);
        console.log(`✅ 已更新 ${positionUpdates.length} 个工具的位置`);
      } else {
        // 即使没有位置变化，也强制保存一次以确保排序被记录
        console.log('⚠️ 没有位置需要更新，但强制保存排序');
        const allUpdates = newOrder.map((tool, index) => ({
          id: tool.id,
          position: index * 1000
        }));
        await toolRegistry.updateToolPositions(allUpdates);
        console.log('✅ 强制更新完成');
      }

      // 重新渲染工具列表
      this.renderTools();

    } catch (error) {
      console.error('重新排序失败:', error);
      notificationManager.error('排序失败，请重试');
    }
  }

  // 打开工具设置
  private openToolSettings(tool: Tool) {
    const modal = this.createToolSettingsModal(tool);
    document.body.appendChild(modal);
  }

  // 创建工具设置模态框
  private createToolSettingsModal(tool: Tool): HTMLElement {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';

    const categories = categoryManager.getAll();
    const categoriesOptions = categories.map(cat => `
      <label class="category-option">
        <input type="checkbox" value="${cat.id}" ${tool.categories?.includes(cat.id) ? 'checked' : ''}>
        <span class="category-option-icon">${cat.icon || '📁'}</span>
        <span class="category-option-name">${cat.name}</span>
      </label>
    `).join('');

    modal.innerHTML = `
      <div class="modal-content tool-settings-modal">
        <div class="modal-header">
          <h3>工具设置 - ${tool.name}</h3>
          <button class="modal-close-btn" type="button">×</button>
        </div>
        <div class="modal-body">
          <div class="setting-section">
            <h4>基本信息</h4>
            <div class="tool-info">
              <div class="tool-info-item">
                <span class="tool-info-label">工具ID:</span>
                <span class="tool-info-value">${tool.id}</span>
              </div>
              <div class="tool-info-item">
                <span class="tool-info-label">版本:</span>
                <span class="tool-info-value">v${tool.version.major}.${tool.version.minor}.${tool.version.patch}</span>
              </div>
              <div class="tool-info-item">
                <span class="tool-info-label">状态:</span>
                <span class="tool-info-value ${tool.enabled ? 'enabled' : 'disabled'}">
                  ${tool.enabled ? '启用' : '禁用'}
                </span>
              </div>
            </div>
          </div>

          <div class="setting-section">
            <h4>分类设置</h4>
            <div class="categories-selector" id="categories-selector">
              ${categoriesOptions}
            </div>
          </div>

          <div class="setting-section">
            <h4>工具控制</h4>
            <div class="tool-controls">
              <label class="toggle-switch">
                <input type="checkbox" id="tool-enabled" ${tool.enabled ? 'checked' : ''}>
                <span class="toggle-slider"></span>
                <span class="toggle-label">启用工具</span>
              </label>
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" id="cancel-btn">取消</button>
          <button class="btn btn-primary" id="save-btn">保存</button>
        </div>
      </div>
    `;

    // 绑定事件
    this.bindToolSettingsEvents(modal, tool);

    return modal;
  }

  // 绑定工具设置事件
  private bindToolSettingsEvents(modal: HTMLElement, tool: Tool) {
    // 关闭模态框
    const closeBtn = modal.querySelector('.modal-close-btn');
    const cancelBtn = modal.querySelector('#cancel-btn');
    const overlay = modal;

    const closeModal = () => {
      document.body.removeChild(modal);
    };

    closeBtn?.addEventListener('click', closeModal);
    cancelBtn?.addEventListener('click', closeModal);
    overlay.addEventListener('click', (e) => {
      if (e.target === overlay) {
        closeModal();
      }
    });

    // 保存设置
    const saveBtn = modal.querySelector('#save-btn');
    saveBtn?.addEventListener('click', async () => {
      try {
        // 获取选中的分类
        const selectedCategories: string[] = [];
        const checkboxes = modal.querySelectorAll('#categories-selector input[type="checkbox"]:checked');
        checkboxes.forEach(checkbox => {
          selectedCategories.push((checkbox as HTMLInputElement).value);
        });

        // 确保至少有一个分类
        if (selectedCategories.length === 0) {
          selectedCategories.push('all');
        }

        // 获取启用状态
        const enabledCheckbox = modal.querySelector('#tool-enabled') as HTMLInputElement;
        const enabled = enabledCheckbox.checked;

        // 更新内存中的工具实例
        const toolInstance = toolRegistry.getById(tool.id);
        if (toolInstance) {
          toolInstance.categories = selectedCategories;
          toolInstance.enabled = enabled;
        }

        // 更新存储中的工具数据
        await toolRegistry.updateTool(tool.id, {
          categories: selectedCategories,
          enabled: enabled
        });

        closeModal();
        notificationManager.success('工具设置已保存');
      } catch (error) {
        console.error('保存工具设置失败:', error);
        notificationManager.error('保存设置失败: ' + (error as Error).message);
      }
    });
  }
}

// 注册工具并保留用户设置
async function registerToolWithSettings(toolInstance: Tool) {
  const existingTool = toolRegistry.getById(toolInstance.id);

  if (existingTool) {
    // 如果工具已存在，先注销旧工具，再注册新工具
    await toolRegistry.unregister(toolInstance.id);
    await toolRegistry.register(toolInstance);
  } else {
    // 如果工具不存在，直接注册
    await toolRegistry.register(toolInstance);
  }
}

// 初始化应用
async function initApp() {
  try {
    // 初始化设置管理器
    await settingsManager.init();

    // 初始化工具注册表
    await toolRegistry.init();

    // 注册实际工具（保留用户设置）
    await registerToolWithSettings(new XuidTool());
    await registerToolWithSettings(new AlertParserTool());
    await registerToolWithSettings(new TaskListTool());

    // 注册API Diff工具
    await registerToolWithSettings(new ApiDiffTool());

    // 创建工具管理器
    new ToolManager();

    console.log('服务运营工具集合已初始化');
  } catch (error) {
    console.error('应用初始化失败:', error);
  }
}

// 等待DOM加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}

// 导出工具注册表供其他模块使用
export { toolRegistry, type Tool };

// 为了调试方便，将管理器导出到全局作用域
if (typeof window !== 'undefined') {
  (window as any).updateManager = updateManager;
  (window as any).settingsManager = settingsManager;
  (window as any).toolRegistry = toolRegistry;
  (window as any).updateNotificationManager = updateNotificationManager;

  // 导入并导出 VersionUtils
  import('../../utils/tool-template').then(module => {
    (window as any).VersionUtils = module.VersionUtils;
  });
}
